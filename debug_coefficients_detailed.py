#!/usr/bin/env python3
"""
Detailed debugging of why After coefficient > Before coefficient in hierarchical model
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from sklearn.preprocessing import StandardScaler

def main():
    # Load and prepare data exactly like in notebook
    df = pd.read_csv('demelted_data.csv')
    
    # Create variables exactly like in notebook
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    
    # Create standardized variables
    scaler = StandardScaler()
    
    df['ABI_Duration_Days'] = (pd.to_datetime(df['ABI End']) - pd.to_datetime(df['ABI Start'])).dt.days
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    
    # Standardize
    for var in ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    # Prepare model data like in notebook
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                 'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'ABI Mechanic', 'Retailer']
    
    df_model = df[model_vars].dropna()
    print(f"Model data shape: {df_model.shape}")
    
    print("\n" + "="*80)
    print("STEP-BY-STEP COEFFICIENT ANALYSIS")
    print("="*80)
    
    # Step 1: Simple regression
    print("\n1. SIMPLE REGRESSION (Before + After only)")
    print("-" * 50)
    
    X_simple = df_model[['Before', 'After']].copy()
    y = df_model['ABI_MS_Uplift_Rel'].copy()
    X_simple_with_intercept = sm.add_constant(X_simple)
    model_simple = sm.OLS(y, X_simple_with_intercept).fit()
    
    print(f"Before: {model_simple.params['Before']:.4f}")
    print(f"After: {model_simple.params['After']:.4f}")
    print(f"Before > After: {model_simple.params['Before'] > model_simple.params['After']}")
    
    # Step 2: Add coverage
    print("\n2. ADD COVERAGE")
    print("-" * 50)
    
    X_with_coverage = df_model[['Before', 'After', 'ABI_Coverage_std']].copy()
    X_with_coverage = sm.add_constant(X_with_coverage)
    model_coverage = sm.OLS(y, X_with_coverage).fit()
    
    print(f"Before: {model_coverage.params['Before']:.4f}")
    print(f"After: {model_coverage.params['After']:.4f}")
    print(f"Before > After: {model_coverage.params['Before'] > model_coverage.params['After']}")
    
    # Check coverage correlation with timing
    print(f"\nCoverage correlation with Before: {df_model['ABI_Coverage_std'].corr(df_model['Before']):.3f}")
    print(f"Coverage correlation with After: {df_model['ABI_Coverage_std'].corr(df_model['After']):.3f}")
    
    # Step 3: Check mechanic distribution
    print("\n3. MECHANIC ANALYSIS")
    print("-" * 50)
    
    print("Mechanic distribution by Before:")
    mechanic_before = pd.crosstab(df_model['ABI Mechanic'], df_model['Before'], normalize='columns')
    print(mechanic_before.round(3))
    
    print("\nMechanic distribution by After:")
    mechanic_after = pd.crosstab(df_model['ABI Mechanic'], df_model['After'], normalize='columns')
    print(mechanic_after.round(3))
    
    # Check uplift by mechanic
    print("\nUplift by mechanic:")
    uplift_by_mechanic = df_model.groupby('ABI Mechanic')['ABI_MS_Uplift_Rel'].agg(['count', 'mean']).round(3)
    print(uplift_by_mechanic)
    
    # Step 4: Add mechanic
    print("\n4. ADD MECHANIC")
    print("-" * 50)
    
    try:
        formula_with_mechanic = "ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + C(ABI_Mechanic)"
        model_mechanic = smf.ols(formula_with_mechanic, data=df_model).fit()
        
        print(f"Before: {model_mechanic.params['Before']:.4f}")
        print(f"After: {model_mechanic.params['After']:.4f}")
        print(f"Before > After: {model_mechanic.params['Before'] > model_mechanic.params['After']}")
        
        print("\nMechanic coefficients:")
        for param in model_mechanic.params.index:
            if 'ABI_Mechanic' in param:
                print(f"{param}: {model_mechanic.params[param]:.4f}")
                
    except Exception as e:
        print(f"Error with mechanic model: {e}")
    
    # Step 5: Full model
    print("\n5. FULL MODEL (like in notebook)")
    print("-" * 50)
    
    try:
        formula_full = """ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + 
                         ABI_Duration_Days_std + Avg_Temp_std + 
                         ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""
        model_full = smf.ols(formula_full, data=df_model).fit()
        
        print(f"Before: {model_full.params['Before']:.4f}")
        print(f"After: {model_full.params['After']:.4f}")
        print(f"Before > After: {model_full.params['Before'] > model_full.params['After']}")
        
        print(f"\nP-values:")
        print(f"Before p-value: {model_full.pvalues['Before']:.4f}")
        print(f"After p-value: {model_full.pvalues['After']:.4f}")
        
    except Exception as e:
        print(f"Error with full model: {e}")
    
    # Step 6: Check for confounding
    print("\n6. CONFOUNDING ANALYSIS")
    print("-" * 50)
    
    # Check if Before/After are correlated with high-uplift mechanics
    print("Average uplift by timing and mechanic:")
    timing_mechanic = df_model.groupby(['Before', 'After', 'ABI Mechanic'])['ABI_MS_Uplift_Rel'].agg(['count', 'mean']).round(3)
    print(timing_mechanic)
    
    print("\n" + "="*80)
    print("CONCLUSION")
    print("="*80)
    print("The issue is likely that certain mechanics (like FID, Immediate, LV)")
    print("are more common in 'After' scenarios and these mechanics have")
    print("higher baseline uplift, causing the model to attribute the")
    print("timing effect incorrectly when controlling for mechanic.")

if __name__ == "__main__":
    main()
