import pandas as pd
import numpy as np

def filter_95th_percentile_outliers(csv_file_path, output_file_path=None):
    """
    Read CSV file and filter out rows where 'ABI MS Promo Uplift - rel' is outside the 95th percentile range.

    Parameters:
    csv_file_path (str): Path to the input CSV file
    output_file_path (str): Path to save the filtered CSV file (optional)

    Returns:
    pandas.DataFrame: Filtered dataframe
    """

    # Read the CSV file
    print(f"Reading data from {csv_file_path}...")
    df = pd.read_csv(csv_file_path)

    print(f"Original dataset shape: {df.shape}")

    # Check if the column exists
    target_column = 'ABI MS Promo Uplift - rel'
    if target_column not in df.columns:
        print(f"Error: Column '{target_column}' not found in the dataset.")
        print(f"Available columns: {list(df.columns)}")
        return None

    # Convert the column to numeric, handling any non-numeric values
    df[target_column] = pd.to_numeric(df[target_column], errors='coerce')

    # Remove rows with NaN values in the target column
    initial_rows = len(df)
    df = df.dropna(subset=[target_column])
    nan_removed = initial_rows - len(df)
    if nan_removed > 0:
        print(f"Removed {nan_removed} rows with NaN values in '{target_column}'")

    # Calculate the 95th percentile range (2.5th to 97.5th percentile)
    lower_bound = np.percentile(df[target_column], 2.5)
    upper_bound = np.percentile(df[target_column], 97.5)

    print(f"\n95th Percentile Range for '{target_column}':")
    print(f"Lower bound (2.5th percentile): {lower_bound:.6f}")
    print(f"Upper bound (97.5th percentile): {upper_bound:.6f}")

    # Count outliers before filtering
    outliers_mask = (df[target_column] < lower_bound) | (df[target_column] > upper_bound)
    outliers_count = outliers_mask.sum()

    print(f"\nOutliers found: {outliers_count} rows ({outliers_count/len(df)*100:.2f}% of data)")

    # Filter the dataframe to keep only rows within the 95th percentile range
    filtered_df = df[(df[target_column] >= lower_bound) & (df[target_column] <= upper_bound)]

    print(f"Filtered dataset shape: {filtered_df.shape}")
    print(f"Rows removed: {len(df) - len(filtered_df)}")

    # Display some statistics
    print(f"\nStatistics for '{target_column}' after filtering:")
    print(f"Min: {filtered_df[target_column].min():.6f}")
    print(f"Max: {filtered_df[target_column].max():.6f}")
    print(f"Mean: {filtered_df[target_column].mean():.6f}")
    print(f"Median: {filtered_df[target_column].median():.6f}")
    print(f"Std: {filtered_df[target_column].std():.6f}")

    # Save the filtered data if output path is provided
    if output_file_path:
        filtered_df.to_csv(output_file_path, index=False)
        print(f"\nFiltered data saved to: {output_file_path}")

    return filtered_df

# Main execution
if __name__ == "__main__":
    # Read and filter the data
    input_file = 'new_test_output_ads_v3.csv'
    output_file = 'new_test_output_ads_v3_filtered_95th.csv'

    # Filter the data
    filtered_data = filter_95th_percentile_outliers(input_file, output_file)

    if filtered_data is not None:
        print("\n" + "="*50)
        print("FILTERING COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"Original file: {input_file}")
        print(f"Filtered file: {output_file}")
        print(f"All columns and metrics preserved, only outlier rows removed.")