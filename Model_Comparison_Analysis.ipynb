{
  "cells": [
    {
      "cell_type": "markdown",
      "metadata": {},
      "source": [
        "# 📊 COMPREHENSIVE MODEL COMPARISON ANALYSIS\n",
        "\n",
        "This notebook provides detailed model comparison metrics and visualizations\n",
        "to support the selection of the optimal hierarchical model for competitive timing analysis.\n",
        "\n",
        "## Key Metrics:\n",
        "- **AIC (Akaike Information Criterion)**: Lower values indicate better model fit\n",
        "- **BIC (Bayesian Information Criterion)**: Lower values indicate better model fit with penalty for complexity\n",
        "- **Log-Likelihood**: Higher values (less negative) indicate better fit\n",
        "\n",
        "---"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Import required libraries\n",
        "import pandas as pd\n",
        "import matplotlib.pyplot as plt\n",
        "import seaborn as sns\n",
        "import numpy as np\n",
        "from scipy import stats\n",
        "import warnings\n",
        "warnings.filterwarnings('ignore')\n",
        "\n",
        "# Set style for professional plots\n",
        "plt.style.use('seaborn-v0_8-whitegrid')\n",
        "sns.set_palette('husl')\n",
        "plt.rcParams['figure.figsize'] = (12, 8)\n",
        "plt.rcParams['font.size'] = 11\n",
        "\n",
        "print('\\n' + '='*60)\n",
        "print('📊 COMPREHENSIVE MODEL COMPARISON ANALYSIS')\n",
        "print('='*60)\n",
        "print('Libraries loaded successfully!')\n",
        "print('Ready to analyze model performance metrics...')"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Create comprehensive model comparison data\n",
        "model_comparison = {\n",
        "    'Model': [\n",
        "        'fixed_only',\n",
        "        'retailer_intercept', \n",
        "        'brand_intercept',\n",
        "        'pack_intercept',\n",
        "        'brand_retailer_crossed',\n",
        "        'retailer_pack_crossed',\n",
        "        'brand_pack_crossed', \n",
        "        'three_way_crossed',\n",
        "        'duration_slopes_retailer',\n",
        "        'duration_slopes_brand',\n",
        "        'duration_slopes_pack',\n",
        "        'coverage_slopes_retailer'\n",
        "    ],\n",
        "    'AIC': [\n",
        "        1620.063266, 1606.295474, 1623.903773, float('inf'),\n",
        "        1579.222702, 1605.787206, 1623.907110, 1581.125277,\n",
        "        1601.784653, 1630.430869, 1645.016017, 1608.439830\n",
        "    ],\n",
        "    'BIC': [\n",
        "        1662.932667, 1660.855591, 1678.463927, float('inf'),\n",
        "        1633.782856, 1660.347360, 1678.467264, 1639.582585,\n",
        "        1671.933423, 1700.579638, 1715.164786, 1678.588599\n",
        "    ],\n",
        "    'Log_Likelihood': [\n",
        "        -799.031633, -789.147718, -797.951887, float('inf'),\n",
        "        -775.611351, -788.893603, -797.953555, -775.562638,\n",
        "        -782.892327, -797.215434, -804.508008, -786.219915\n",
        "    ]\n",
        "}\n",
        "\n",
        "# Create DataFrame\n",
        "comparison_df = pd.DataFrame(model_comparison)\n",
        "\n",
        "# Replace inf values with NaN for better handling\n",
        "comparison_df = comparison_df.replace([np.inf, -np.inf], np.nan)\n",
        "\n",
        "print('\\nModel comparison data loaded successfully!')\n",
        "print(f'Total models analyzed: {len(comparison_df)}')\n",
        "print(f'Models with valid results: {comparison_df.dropna().shape[0]}')"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {},
      "source": [
        "## 📋 MODEL COMPARISON TABLE\n",
        "\n",
        "The following table shows the key performance metrics for all tested hierarchical models:"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Display formatted model comparison table\n",
        "print('\\nMODEL COMPARISON:')\n",
        "print('-' * 70)\n",
        "print(f\"{'Model':<25} {'AIC':<12} {'BIC':<12} {'Log-Likelihood':<15}\")\n",
        "print('-' * 70)\n",
        "\n",
        "for idx, row in comparison_df.iterrows():\n",
        "    model_name = row['Model']\n",
        "    aic = '-inf' if pd.isna(row['AIC']) else f\"{row['AIC']:.6f}\"\n",
        "    bic = '-inf' if pd.isna(row['BIC']) else f\"{row['BIC']:.6f}\"\n",
        "    ll = 'inf' if pd.isna(row['Log_Likelihood']) else f\"{row['Log_Likelihood']:.6f}\"\n",
        "    print(f\"{model_name:<25} {aic:<12} {bic:<12} {ll:<15}\")\n",
        "\n",
        "print('-' * 70)\n",
        "\n",
        "# Identify best models\n",
        "valid_df = comparison_df.dropna()\n",
        "best_aic_idx = valid_df['AIC'].idxmin()\n",
        "best_bic_idx = valid_df['BIC'].idxmin() \n",
        "best_ll_idx = valid_df['Log_Likelihood'].idxmax()\n",
        "\n",
        "print('\\n🏆 BEST PERFORMING MODELS:')\n",
        "print(f\"Best AIC: {comparison_df.loc[best_aic_idx, 'Model']} ({comparison_df.loc[best_aic_idx, 'AIC']:.6f})\")\n",
        "print(f\"Best BIC: {comparison_df.loc[best_bic_idx, 'Model']} ({comparison_df.loc[best_bic_idx, 'BIC']:.6f})\")\n",
        "print(f\"Best Log-Likelihood: {comparison_df.loc[best_ll_idx, 'Model']} ({comparison_df.loc[best_ll_idx, 'Log_Likelihood']:.6f})\")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {},
      "source": [
        "## 📈 VISUALIZATION CHARTS\n",
        "\n",
        "The following charts provide visual comparison of model performance across different metrics:"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Create comprehensive visualization plots\n",
        "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n",
        "fig.suptitle('📊 Comprehensive Model Comparison Analysis', fontsize=16, fontweight='bold')\n",
        "\n",
        "# Prepare data for plotting (exclude inf values)\n",
        "plot_df = comparison_df.dropna().copy()\n",
        "plot_df['Model_Short'] = plot_df['Model'].str.replace('_', '\\n', 1)  # Better labels\n",
        "\n",
        "# 1. AIC Comparison\n",
        "ax1 = axes[0, 0]\n",
        "bars1 = ax1.bar(range(len(plot_df)), plot_df['AIC'], color='skyblue', alpha=0.7)\n",
        "ax1.set_title('AIC Comparison (Lower is Better)', fontweight='bold')\n",
        "ax1.set_xlabel('Models')\n",
        "ax1.set_ylabel('AIC Value')\n",
        "ax1.set_xticks(range(len(plot_df)))\n",
        "ax1.set_xticklabels(plot_df['Model_Short'], rotation=45, ha='right', fontsize=9)\n",
        "ax1.grid(True, alpha=0.3)\n",
        "\n",
        "# Highlight best AIC\n",
        "best_aic_pos = plot_df['AIC'].idxmin()\n",
        "best_aic_bar_idx = plot_df.index.get_loc(best_aic_pos)\n",
        "bars1[best_aic_bar_idx].set_color('gold')\n",
        "bars1[best_aic_bar_idx].set_alpha(1.0)\n",
        "\n",
        "# 2. BIC Comparison  \n",
        "ax2 = axes[0, 1]\n",
        "bars2 = ax2.bar(range(len(plot_df)), plot_df['BIC'], color='lightcoral', alpha=0.7)\n",
        "ax2.set_title('BIC Comparison (Lower is Better)', fontweight='bold')\n",
        "ax2.set_xlabel('Models')\n",
        "ax2.set_ylabel('BIC Value')\n",
        "ax2.set_xticks(range(len(plot_df)))\n",
        "ax2.set_xticklabels(plot_df['Model_Short'], rotation=45, ha='right', fontsize=9)\n",
        "ax2.grid(True, alpha=0.3)\n",
        "\n",
        "# Highlight best BIC\n",
        "best_bic_pos = plot_df['BIC'].idxmin()\n",
        "best_bic_bar_idx = plot_df.index.get_loc(best_bic_pos)\n",
        "bars2[best_bic_bar_idx].set_color('gold')\n",
        "bars2[best_bic_bar_idx].set_alpha(1.0)\n",
        "\n",
        "print('Charts 1-2 created: AIC and BIC comparisons')"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Continue with remaining charts\n",
        "# 3. Log-Likelihood Comparison\n",
        "ax3 = axes[1, 0]\n",
        "bars3 = ax3.bar(range(len(plot_df)), plot_df['Log_Likelihood'], color='lightgreen', alpha=0.7)\n",
        "ax3.set_title('Log-Likelihood Comparison (Higher is Better)', fontweight='bold')\n",
        "ax3.set_xlabel('Models')\n",
        "ax3.set_ylabel('Log-Likelihood Value')\n",
        "ax3.set_xticks(range(len(plot_df)))\n",
        "ax3.set_xticklabels(plot_df['Model_Short'], rotation=45, ha='right', fontsize=9)\n",
        "ax3.grid(True, alpha=0.3)\n",
        "\n",
        "# Highlight best Log-Likelihood\n",
        "best_ll_pos = plot_df['Log_Likelihood'].idxmax()\n",
        "best_ll_bar_idx = plot_df.index.get_loc(best_ll_pos)\n",
        "bars3[best_ll_bar_idx].set_color('gold')\n",
        "bars3[best_ll_bar_idx].set_alpha(1.0)\n",
        "\n",
        "# 4. Combined Ranking Chart\n",
        "ax4 = axes[1, 1]\n",
        "\n",
        "# Calculate rankings (1 = best)\n",
        "plot_df['AIC_Rank'] = plot_df['AIC'].rank()\n",
        "plot_df['BIC_Rank'] = plot_df['BIC'].rank()\n",
        "plot_df['LL_Rank'] = plot_df['Log_Likelihood'].rank(ascending=False)\n",
        "plot_df['Average_Rank'] = (plot_df['AIC_Rank'] + plot_df['BIC_Rank'] + plot_df['LL_Rank']) / 3\n",
        "\n",
        "# Plot average ranking\n",
        "bars4 = ax4.bar(range(len(plot_df)), plot_df['Average_Rank'], color='mediumpurple', alpha=0.7)\n",
        "ax4.set_title('Overall Model Ranking (Lower is Better)', fontweight='bold')\n",
        "ax4.set_xlabel('Models')\n",
        "ax4.set_ylabel('Average Rank')\n",
        "ax4.set_xticks(range(len(plot_df)))\n",
        "ax4.set_xticklabels(plot_df['Model_Short'], rotation=45, ha='right', fontsize=9)\n",
        "ax4.grid(True, alpha=0.3)\n",
        "\n",
        "# Highlight best overall\n",
        "best_overall_pos = plot_df['Average_Rank'].idxmin()\n",
        "best_overall_bar_idx = plot_df.index.get_loc(best_overall_pos)\n",
        "bars4[best_overall_bar_idx].set_color('gold')\n",
        "bars4[best_overall_bar_idx].set_alpha(1.0)\n",
        "\n",
        "plt.tight_layout()\n",
        "plt.show()\n",
        "\n",
        "print('Charts 3-4 created: Log-Likelihood and Overall Ranking')\n",
        "print('\\n✅ All visualization charts completed!')"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {},
      "source": [
        "## 📊 DETAILED RANKING ANALYSIS\n",
        "\n",
        "This section provides detailed ranking analysis across all metrics:"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Create detailed ranking table\n",
        "ranking_df = plot_df[['Model', 'AIC', 'BIC', 'Log_Likelihood', 'AIC_Rank', 'BIC_Rank', 'LL_Rank', 'Average_Rank']].copy()\n",
        "ranking_df = ranking_df.sort_values('Average_Rank')\n",
        "\n",
        "print('\\n📊 DETAILED MODEL RANKING ANALYSIS')\n",
        "print('=' * 80)\n",
        "print(f\"{'Rank':<4} {'Model':<25} {'AIC':<12} {'BIC':<12} {'Log-Lik':<12} {'Avg Rank':<10}\")\n",
        "print('-' * 80)\n",
        "\n",
        "for i, (idx, row) in enumerate(ranking_df.iterrows(), 1):\n",
        "    print(f\"{i:<4} {row['Model']:<25} {row['AIC']:<12.2f} {row['BIC']:<12.2f} {row['Log_Likelihood']:<12.2f} {row['Average_Rank']:<10.2f}\")\n",
        "\n",
        "print('-' * 80)\n",
        "\n",
        "# Statistical summary\n",
        "print('\\n📈 STATISTICAL SUMMARY:')\n",
        "print(f\"Best overall model: {ranking_df.iloc[0]['Model']}\")\n",
        "print(f\"Worst overall model: {ranking_df.iloc[-1]['Model']}\")\n",
        "print(f\"AIC range: {plot_df['AIC'].min():.2f} - {plot_df['AIC'].max():.2f}\")\n",
        "print(f\"BIC range: {plot_df['BIC'].min():.2f} - {plot_df['BIC'].max():.2f}\")\n",
        "print(f\"Log-Likelihood range: {plot_df['Log_Likelihood'].min():.2f} - {plot_df['Log_Likelihood'].max():.2f}\")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {},
      "source": [
        "## 🎯 MODEL SELECTION RECOMMENDATIONS\n",
        "\n",
        "Based on the comprehensive analysis above:"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "# Generate model selection recommendations\n",
        "best_model = ranking_df.iloc[0]['Model']\n",
        "best_aic_model = plot_df.loc[plot_df['AIC'].idxmin(), 'Model']\n",
        "best_bic_model = plot_df.loc[plot_df['BIC'].idxmin(), 'Model']\n",
        "best_ll_model = plot_df.loc[plot_df['Log_Likelihood'].idxmax(), 'Model']\n",
        "\n",
        "print('\\n🎯 MODEL SELECTION RECOMMENDATIONS')\n",
        "print('=' * 60)\n",
        "\n",
        "print(f\"\\n🏆 OVERALL WINNER: {best_model}\")\n",
        "print(f\"   Average Rank: {ranking_df.iloc[0]['Average_Rank']:.2f}\")\n",
        "print(f\"   This model provides the best balance across all metrics.\")\n",
        "\n",
        "print(f\"\\n📊 METRIC-SPECIFIC WINNERS:\")\n",
        "print(f\"   Best AIC: {best_aic_model}\")\n",
        "print(f\"   Best BIC: {best_bic_model}\")\n",
        "print(f\"   Best Log-Likelihood: {best_ll_model}\")\n",
        "\n",
        "# Check if there's consensus\n",
        "consensus_models = {best_aic_model, best_bic_model, best_ll_model}\n",
        "if len(consensus_models) == 1:\n",
        "    print(f\"\\n✅ STRONG CONSENSUS: All metrics agree on {list(consensus_models)[0]}\")\n",
        "elif len(consensus_models) == 2:\n",
        "    print(f\"\\n⚖️ PARTIAL CONSENSUS: Two metrics agree, consider both models\")\n",
        "else:\n",
        "    print(f\"\\n⚠️ NO CONSENSUS: Different metrics favor different models\")\n",
        "    print(f\"   Recommend using overall ranking or domain expertise for final selection\")\n",
        "\n",
        "print(f\"\\n💡 BUSINESS RECOMMENDATION:\")\n",
        "if best_model == 'brand_retailer_crossed':\n",
        "    print(f\"   The {best_model} model captures important interaction effects\")\n",
        "    print(f\"   between brands and retailers, making it ideal for competitive analysis.\")\n",
        "else:\n",
        "    print(f\"   The {best_model} model provides the optimal balance of\")\n",
        "    print(f\"   model fit and complexity for your analysis needs.\")\n",
        "\n",
        "print('\\n' + '=' * 60)\n",
        "print('📋 Analysis completed successfully!')\n",
        "print('Use these results to guide your model selection decision.')"
      ]
    }
  ],""
  "metadata": {
    "kernelspec": {
      "display_name": "base",
      "language": "python", 
      "name": "python3"
    },
    "language_info": {
      "codemirror_mode": {
        "name": "ipython",
        "version": 3
      },
      "file_extension": ".py",
      "mimetype": "text/x-python",
      "name": "python",
      "nbconvert_exporter": "python",
      "pygments_lexer": "ipython3",
      "version": "3.10.16"
    }
  },
  "nbformat": 4,
  "nbformat_minor": 4
}
