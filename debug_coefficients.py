#!/usr/bin/env python3
"""
Debug script to understand why 1 wk before has lower coefficients than 1 wk after
despite showing better raw uplift.
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy import stats

def main():
    # Load the data
    df = pd.read_csv('demelted_data.csv')
    
    print("=" * 80)
    print("DEBUGGING COEFFICIENT DIRECTION ISSUE")
    print("=" * 80)
    
    # Basic data overview
    print(f"\nTotal observations: {len(df)}")
    
    # Check raw uplift differences
    print("\n1. RAW UPLIFT ANALYSIS")
    print("-" * 40)
    
    before_0 = df[df['1 wk before'] == 0]['ABI MS Promo Uplift - rel']
    before_1 = df[df['1 wk before'] == 1]['ABI MS Promo Uplift - rel']
    after_0 = df[df['1 wk after'] == 0]['ABI MS Promo Uplift - rel']
    after_1 = df[df['1 wk after'] == 1]['ABI MS Promo Uplift - rel']
    
    print(f"1 wk before = 0: Mean = {before_0.mean():.4f}, N = {len(before_0)}")
    print(f"1 wk before = 1: Mean = {before_1.mean():.4f}, N = {len(before_1)}")
    print(f"Raw difference (1 wk before): {before_1.mean() - before_0.mean():.4f}")
    
    print(f"\n1 wk after = 0: Mean = {after_0.mean():.4f}, N = {len(after_0)}")
    print(f"1 wk after = 1: Mean = {after_1.mean():.4f}, N = {len(after_1)}")
    print(f"Raw difference (1 wk after): {after_1.mean() - after_0.mean():.4f}")
    
    # Simple regression (no controls)
    print("\n2. SIMPLE REGRESSION (NO CONTROLS)")
    print("-" * 40)
    
    X_simple = df[['1 wk before', '1 wk after']].copy()
    y = df['ABI MS Promo Uplift - rel'].copy()
    
    # Remove NaN values
    mask = ~(X_simple.isna().any(axis=1) | y.isna())
    X_clean = X_simple[mask]
    y_clean = y[mask]
    
    # Add intercept and fit
    X_with_intercept = sm.add_constant(X_clean)
    model_simple = sm.OLS(y_clean, X_with_intercept).fit()
    
    print(f"1 wk before coefficient: {model_simple.params['1 wk before']:.4f}")
    print(f"1 wk after coefficient: {model_simple.params['1 wk after']:.4f}")
    print(f"1 wk before p-value: {model_simple.pvalues['1 wk before']:.4f}")
    print(f"1 wk after p-value: {model_simple.pvalues['1 wk after']:.4f}")
    
    # Check Same Week variable impact
    print("\n3. SAME WEEK VARIABLE ANALYSIS")
    print("-" * 40)
    
    print("Same Week distribution:")
    print(df['Same Week'].value_counts().sort_index())
    
    same_week_0 = df[df['Same Week'] == 0]['ABI MS Promo Uplift - rel']
    same_week_1 = df[df['Same Week'] == 1]['ABI MS Promo Uplift - rel']
    
    print(f"\nSame Week = 0: Mean = {same_week_0.mean():.4f}, N = {len(same_week_0)}")
    print(f"Same Week = 1: Mean = {same_week_1.mean():.4f}, N = {len(same_week_1)}")
    print(f"Same Week difference: {same_week_1.mean() - same_week_0.mean():.4f}")
    
    # Correlation between timing variables
    print("\n4. TIMING VARIABLE CORRELATIONS")
    print("-" * 40)
    
    timing_vars = df[['Same Week', '1 wk before', '1 wk after']].copy()
    corr_matrix = timing_vars.corr()
    print("Correlation matrix:")
    print(corr_matrix.round(3))
    
    # Regression with Same Week included
    print("\n5. REGRESSION WITH SAME WEEK INCLUDED")
    print("-" * 40)
    
    X_with_same = df[['Same Week', '1 wk before', '1 wk after']].copy()
    
    # Remove NaN values
    mask = ~(X_with_same.isna().any(axis=1) | y.isna())
    X_clean = X_with_same[mask]
    y_clean = y[mask]
    
    # Add intercept and fit
    X_with_intercept = sm.add_constant(X_clean)
    model_with_same = sm.OLS(y_clean, X_with_intercept).fit()
    
    print(f"Same Week coefficient: {model_with_same.params['Same Week']:.4f}")
    print(f"1 wk before coefficient: {model_with_same.params['1 wk before']:.4f}")
    print(f"1 wk after coefficient: {model_with_same.params['1 wk after']:.4f}")
    
    print(f"\nP-values:")
    print(f"Same Week p-value: {model_with_same.pvalues['Same Week']:.4f}")
    print(f"1 wk before p-value: {model_with_same.pvalues['1 wk before']:.4f}")
    print(f"1 wk after p-value: {model_with_same.pvalues['1 wk after']:.4f}")
    
    # Cross-tabulation analysis
    print("\n6. CROSS-TABULATION ANALYSIS")
    print("-" * 40)
    
    # Same Week vs Before/After
    print("Same Week vs 1 wk before:")
    crosstab_same_before = pd.crosstab(df['Same Week'], df['1 wk before'], margins=True)
    print(crosstab_same_before)
    
    print("\nSame Week vs 1 wk after:")
    crosstab_same_after = pd.crosstab(df['Same Week'], df['1 wk after'], margins=True)
    print(crosstab_same_after)
    
    # Detailed breakdown by combinations
    print("\n7. DETAILED COMBINATION ANALYSIS")
    print("-" * 40)
    
    df['timing_combo'] = (df['Same Week'].astype(str) + '_' + 
                         df['1 wk before'].astype(str) + '_' + 
                         df['1 wk after'].astype(str))
    
    combo_analysis = df.groupby('timing_combo')['ABI MS Promo Uplift - rel'].agg(['count', 'mean', 'std']).round(4)
    print("Uplift by timing combination (Same_Before_After):")
    print(combo_analysis)
    
    # Check for suppression effect
    print("\n8. SUPPRESSION EFFECT ANALYSIS")
    print("-" * 40)
    
    print("This could be a statistical suppression effect where:")
    print("- Same Week has a very strong positive effect")
    print("- Same Week is correlated with Before/After variables")
    print("- When Same Week is included, it 'suppresses' the Before effect")
    print("- Making the Before coefficient smaller than expected")
    
    # Calculate partial correlations
    print(f"\nCorrelation between Same Week and 1 wk before: {df['Same Week'].corr(df['1 wk before']):.3f}")
    print(f"Correlation between Same Week and 1 wk after: {df['Same Week'].corr(df['1 wk after']):.3f}")
    
    print("\n" + "=" * 80)
    print("CONCLUSION")
    print("=" * 80)
    print("The issue is likely due to the 'Same Week' variable acting as a suppressor.")
    print("When competitor promotions happen in the same week, uplift is very high.")
    print("This variable is correlated with the Before/After timing variables,")
    print("causing the regression to attribute most of the timing effect to 'Same Week'")
    print("rather than to the individual Before/After variables.")

if __name__ == "__main__":
    main()
