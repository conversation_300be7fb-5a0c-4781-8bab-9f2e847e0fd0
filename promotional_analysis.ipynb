import pandas as pd

df = pd.read_csv('new_test_output_ads_v3_filtered_95th.csv')

timing_cols = ['Overlapping', 'Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
for col in timing_cols:
    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)

agg_dict = {
    'Retailer': 'first',
    'ABI SKU': 'first',
    'ABI Start': 'first',
    'ABI End': 'first',
    'ABI Coverage': 'first',
    'ABI Mechanic': 'first',
    'ABI Depth': 'first',
    'ABI Rounded': 'first',
    'Competitor SKU': 'first',
    'Competitor Coverage': 'max',
    'Competitor Mechanic': 'first',
    'Competitor Depth': 'max',
    'Overlapping': 'max',
    'Same Week': 'max',
    '1 wk after': 'max',
    '2 wk after': 'max',
    '1 wk before': 'max',
    '2 wk before': 'max',
    'Avg Temp': 'max',
    'KSM': 'max',
    'overlapping days': 'max',
    'ABI MS Promo': 'first',
    'ABI MS Base': 'first',
    'ABI MS Promo Uplift - abs': 'first',
    'ABI MS Promo Uplift - rel': 'max',
    'base_ms_weeks': 'first',
    'ABI Promo PTC/HL Index': 'first',
    'Comp Promo PTC/HL': 'max',
    'ABI Base W_Distribution': 'first',
    'ABI Base Num_Distribution': 'first',
    'Comp W_Distribution': 'max',
    'Comp Num_Distribution': 'max',
    'ABI_Promo_W_W_Distribution': 'first',
    'ABI_Promo_W_Num_Distribution': 'first',
    'ABI Promo PTC Agg': 'first',
    'Segment Promo PTC Agg': 'first',
    'ABI vs Segment PTC Index Agg': 'max'
}

df_demelted = df.groupby('ABI PromoID').agg(agg_dict).reset_index()

df_demelted.head()



#exporting demelted data
df_demelted.to_csv('demelted_data.csv', index=False)