#!/usr/bin/env python3
"""
Create the correct model with timing-mechanic interactions
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from sklearn.preprocessing import StandardScaler

def main():
    # Load and prepare data
    df = pd.read_csv('demelted_data.csv')
    
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    
    # Create standardized variables
    scaler = StandardScaler()
    df['ABI_Duration_Days'] = (pd.to_datetime(df['ABI End']) - pd.to_datetime(df['ABI Start'])).dt.days
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    
    for var in ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    # Fix column name issue
    df['ABI_Mechanic'] = df['ABI Mechanic']  # Create version without space
    df['KSM'] = df['KSM'].astype(int)
    
    # Prepare model data
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                 'Before', 'After', 'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 
                 'ABI_Mechanic', 'Retailer', 'KSM']
    
    df_model = df[model_vars].dropna()
    print(f"Model data shape: {df_model.shape}")
    
    print("\n" + "="*80)
    print("CORRECT MODEL WITH TIMING-MECHANIC INTERACTIONS")
    print("="*80)
    
    # Model 1: Main effects only (your current problematic model)
    print("\n1. MAIN EFFECTS ONLY (Current problematic model)")
    print("-" * 60)
    
    try:
        formula_main = """ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + 
                         ABI_Duration_Days_std + Avg_Temp_std + 
                         ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + KSM"""
        
        model_main = smf.ols(formula_main, data=df_model).fit()
        
        print(f"Before coefficient: {model_main.params['Before']:.4f} (p={model_main.pvalues['Before']:.3f})")
        print(f"After coefficient: {model_main.params['After']:.4f} (p={model_main.pvalues['After']:.3f})")
        print(f"Before > After: {model_main.params['Before'] > model_main.params['After']}")
        print(f"R-squared: {model_main.rsquared:.3f}")
        
        print("\nPROBLEM: This averages across mechanics, hiding the true relationships!")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Model 2: With timing-mechanic interactions (CORRECT MODEL)
    print("\n2. WITH TIMING-MECHANIC INTERACTIONS (Correct model)")
    print("-" * 60)
    
    try:
        formula_interaction = """ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + 
                               ABI_Duration_Days_std + Avg_Temp_std + 
                               ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + KSM +
                               Before:C(ABI_Mechanic) + After:C(ABI_Mechanic)"""
        
        model_interaction = smf.ols(formula_interaction, data=df_model).fit()
        
        print(f"R-squared: {model_interaction.rsquared:.3f}")
        print(f"Improvement in R-squared: {model_interaction.rsquared - model_main.rsquared:.3f}")
        
        print("\nMain timing effects (baseline for reference mechanic):")
        print(f"Before coefficient: {model_interaction.params['Before']:.4f} (p={model_interaction.pvalues['Before']:.3f})")
        print(f"After coefficient: {model_interaction.params['After']:.4f} (p={model_interaction.pvalues['After']:.3f})")
        
        print("\nTiming-Mechanic Interactions:")
        for param in model_interaction.params.index:
            if 'Before:C(ABI_Mechanic)' in param or 'After:C(ABI_Mechanic)' in param:
                print(f"{param}: {model_interaction.params[param]:.4f} (p={model_interaction.pvalues[param]:.3f})")
        
        # Calculate actual effects for each mechanic
        print("\n3. ACTUAL TIMING EFFECTS BY MECHANIC")
        print("-" * 60)
        
        mechanics = df_model['ABI_Mechanic'].unique()
        
        for mechanic in mechanics:
            print(f"\n{mechanic}:")
            
            # Before effect for this mechanic
            before_effect = model_interaction.params['Before']
            if f'Before:C(ABI_Mechanic)[T.{mechanic}]' in model_interaction.params.index:
                before_effect += model_interaction.params[f'Before:C(ABI_Mechanic)[T.{mechanic}]']
            
            # After effect for this mechanic  
            after_effect = model_interaction.params['After']
            if f'After:C(ABI_Mechanic)[T.{mechanic}]' in model_interaction.params.index:
                after_effect += model_interaction.params[f'After:C(ABI_Mechanic)[T.{mechanic}]']
            
            print(f"  Before effect: {before_effect:.4f}")
            print(f"  After effect: {after_effect:.4f}")
            print(f"  Before > After: {before_effect > after_effect}")
            
            # Count observations
            mech_data = df_model[df_model['ABI_Mechanic'] == mechanic]
            before_count = mech_data['Before'].sum()
            after_count = mech_data['After'].sum()
            print(f"  Sample: {len(mech_data)} total, {before_count} before, {after_count} after")
        
        print("\n4. BUSINESS INTERPRETATION")
        print("-" * 60)
        print("The timing effect depends on your promotion mechanic:")
        print("- Different mechanics respond differently to competitor timing")
        print("- This explains why the simple average was misleading")
        print("- You need to consider mechanic when planning timing strategy")
        
        # Model comparison
        print("\n5. MODEL COMPARISON")
        print("-" * 60)
        print(f"Main effects model AIC: {model_main.aic:.2f}")
        print(f"Interaction model AIC: {model_interaction.aic:.2f}")
        print(f"AIC improvement: {model_main.aic - model_interaction.aic:.2f}")
        
        if model_interaction.aic < model_main.aic:
            print("✅ Interaction model is significantly better!")
        else:
            print("❌ Interaction model not better")
            
        # F-test for interactions
        from scipy import stats
        f_stat = ((model_interaction.ssr - model_main.ssr) / (model_interaction.df_model - model_main.df_model)) / (model_main.ssr / model_main.df_resid)
        f_p = 1 - stats.f.cdf(f_stat, model_interaction.df_model - model_main.df_model, model_main.df_resid)
        
        print(f"\nF-test for interactions: F={f_stat:.3f}, p={f_p:.3f}")
        if f_p < 0.05:
            print("✅ Interactions are statistically significant!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
