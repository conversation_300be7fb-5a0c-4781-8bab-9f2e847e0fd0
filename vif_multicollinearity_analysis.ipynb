{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# VIF and Multicollinearity Analysis\n", "This notebook calculates Variance Inflation Factor (VIF) to assess multicollinearity among selected features."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from statsmodels.stats.outliers_influence import variance_inflation_factor\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["file_path = 'demelted_data.csv'\n", "df = pd.read_csv(file_path)\n", "df.columns = df.columns.str.strip()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Select and Preprocess Columns"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Rename columns for consistency\n", "col_map = {\n", "    'ABI Coverage': 'ABI_Coverage',\n", "    'ABI Mechanic': 'ABI_Mechanic',\n", "    'Overlapping': 'Overlapping',\n", "    'Same Week': 'Same_Week',\n", "    '1 wk after': '1wk_after',\n", "    '2 wk after': '2wk_after',\n", "    '1 wk before': '1wk_before',\n", "    '2 wk before': '2wk_before',\n", "    'Avg Temp': 'Avg_Temp',\n", "    'ABI vs Segment PTC Index Agg': 'ABI_vs_Segment_PTC_Index_Agg'\n", "}\n", "df = df.rename(columns=col_map)\n", "# Combine 'after' and 'before' columns\n", "# df['After'] = df[['1wk_after', '2wk_after']].apply(pd.to_numeric, errors='coerce').sum(axis=1)\n", "# df['Before'] = df[['1wk_before', '2wk_before']].apply(pd.to_numeric, errors='coerce').sum(axis=1)\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Select relevant columns\n", "cols = [\n", "    'ABI_Mechanic',\n", "    'Overlapping',\n", "    'Same_Week',\n", "    # 'After',\n", "    # 'Before',\n", "    '1wk_after',\n", "    '2wk_after',\n", "    '1wk_before',\n", "    '2wk_before',\n", "    'Avg_Temp',\n", "    'ABI_vs_Segment_PTC_Index_Agg',\n", "    'ABI_Coverage'\n", "]\n", "df_vif = df[cols].copy()\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Encode categorical variables\n", "for col in ['ABI_Mechanic', 'Same_Week']:\n", "    if df_vif[col].dtype == 'object':\n", "        le = LabelEncoder()\n", "        df_vif[col] = le.fit_transform(df_vif[col].astype(str))\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data shape: (364, 10)\n", "Data types:\n", "ABI_Mechanic                      int32\n", "Overlapping                       int64\n", "Same_Week                         int64\n", "1wk_after                         int64\n", "2wk_after                         int64\n", "1wk_before                        int64\n", "2wk_before                        int64\n", "Avg_Temp                        float64\n", "ABI_vs_Segment_PTC_Index_Agg    float64\n", "ABI_Coverage                    float64\n", "dtype: object\n", "First few rows:\n", "   ABI_Mechanic  Overlapping  Same_Week  1wk_after  2wk_after  1wk_before  \\\n", "0             0            0          1          1          1           0   \n", "1             1            0          0          0          0           0   \n", "2             2            1          0          1          1           0   \n", "3             0            0          0          0          1           0   \n", "4             0            1          0          0          0           1   \n", "\n", "   2wk_before   Avg_Temp  ABI_vs_Segment_PTC_Index_Agg  ABI_Coverage  \n", "0           1  17.828236                      1.026418      0.787546  \n", "1           0  12.240606                      0.704060      0.733100  \n", "2           1   9.971459                      0.811679      0.801471  \n", "3           1  24.506825                      0.929391      0.763636  \n", "4           1  17.357177                      1.068531      0.846715  \n"]}], "source": ["# Ensure all columns are numeric and clean data\n", "for col in df_vif.columns:\n", "    df_vif[col] = pd.to_numeric(df_vif[col], errors='coerce')\n", "\n", "# Drop rows with any NaN values\n", "df_vif = df_vif.dropna()\n", "\n", "# Check data types and shape\n", "print(f\"Data shape: {df_vif.shape}\")\n", "print(f\"Data types:\\n{df_vif.dtypes}\")\n", "print(f\"First few rows:\\n{df_vif.head()}\")\n", "\n", "# Ensure we have enough data points\n", "if df_vif.shape[0] < df_vif.shape[1]:\n", "    print(f\"Warning: Not enough observations ({df_vif.shape[0]}) for {df_vif.shape[1]} variables\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate VIF"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "VIF Results:\n", "==================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature</th>\n", "      <th>VIF</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Same_Week</td>\n", "      <td>1.601632</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1wk_after</td>\n", "      <td>1.422930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Overlapping</td>\n", "      <td>1.409904</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1wk_before</td>\n", "      <td>1.375070</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2wk_before</td>\n", "      <td>1.155283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Avg_Temp</td>\n", "      <td>1.154840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2wk_after</td>\n", "      <td>1.141063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>ABI_vs_Segment_PTC_Index_Agg</td>\n", "      <td>1.103656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ABI_Mechanic</td>\n", "      <td>1.079893</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>ABI_Coverage</td>\n", "      <td>1.042760</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        feature       VIF\n", "2                     Same_Week  1.601632\n", "3                     1wk_after  1.422930\n", "1                   Overlapping  1.409904\n", "5                    1wk_before  1.375070\n", "6                    2wk_before  1.155283\n", "7                      Avg_Temp  1.154840\n", "4                     2wk_after  1.141063\n", "8  ABI_vs_Segment_PTC_Index_Agg  1.103656\n", "0                  ABI_Mechanic  1.079893\n", "9                  ABI_Coverage  1.042760"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Add constant term for VIF calculation\n", "from statsmodels.tools import add_constant\n", "\n", "# Convert to numpy array and ensure it's float64\n", "X = df_vif.values.astype(np.float64)\n", "\n", "# Add constant (intercept) term\n", "X_with_const = add_constant(X)\n", "\n", "# Calculate VIF for each feature (excluding the constant)\n", "vif_data = pd.DataFrame()\n", "vif_data['feature'] = df_vif.columns\n", "vif_data['VIF'] = [variance_inflation_factor(X_with_const, i+1) for i in range(len(df_vif.columns))]\n", "vif_data = vif_data.sort_values('VIF', ascending=False)\n", "\n", "print(f\"\\nVIF Results:\")\n", "print(\"=\" * 50)\n", "vif_data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Multicollinearity Assessment:\n", "==================================================\n", "VIF Interpretation:\n", "- VIF = 1: No multicollinearity\n", "- 1 < VIF < 5: Moderate multicollinearity\n", "- 5 <= VIF < 10: High multicollinearity\n", "- VIF >= 10: Very high multicollinearity (problematic)\n", "\n", "All Features VIF Summary:\n", "  Same_Week                      VIF:     1.60 (MODERATE)\n", "  1wk_after                      VIF:     1.42 (MODERATE)\n", "  Overlapping                    VIF:     1.41 (MODERATE)\n", "  1wk_before                     VIF:     1.38 (MODERATE)\n", "  2wk_before                     VIF:     1.16 (MODERATE)\n", "  Avg_Temp                       VIF:     1.15 (MODERATE)\n", "  2wk_after                      VIF:     1.14 (MODERATE)\n", "  ABI_vs_Segment_PTC_Index_Agg   VIF:     1.10 (MODERATE)\n", "  ABI_Mechanic                   VIF:     1.08 (MODERATE)\n", "  ABI_Coverage                   VIF:     1.04 (MODERATE)\n"]}], "source": ["# Detailed multicollinearity analysis\n", "print(\"Multicollinearity Assessment:\")\n", "print(\"=\" * 50)\n", "print(\"VIF Interpretation:\")\n", "print(\"- VIF = 1: No multicollinearity\")\n", "print(\"- 1 < VIF < 5: Moderate multicollinearity\")\n", "print(\"- 5 <= VIF < 10: High multicollinearity\")\n", "print(\"- VIF >= 10: Very high multicollinearity (problematic)\")\n", "print()\n", "\n", "high_vif = vif_data[vif_data['VIF'] >= 10]\n", "moderate_vif = vif_data[(vif_data['VIF'] >= 5) & (vif_data['VIF'] < 10)]\n", "\n", "if len(high_vif) > 0:\n", "    print(\"Features with Very High Multicollinearity (VIF >= 10):\")\n", "    for _, row in high_vif.iterrows():\n", "        print(f\"  - {row['feature']}: VIF = {row['VIF']:.2f}\")\n", "    print()\n", "\n", "if len(moderate_vif) > 0:\n", "    print(\"Features with High Multicollinearity (5 <= VIF < 10):\")\n", "    for _, row in moderate_vif.iterrows():\n", "        print(f\"  - {row['feature']}: VIF = {row['VIF']:.2f}\")\n", "    print()\n", "\n", "print(\"All Features VIF Summary:\")\n", "for _, row in vif_data.iterrows():\n", "    status = \"PROBLEMATIC\" if row['VIF'] >= 10 else \"HIGH\" if row['VIF'] >= 5 else \"MODERATE\" if row['VIF'] > 1 else \"GOOD\"\n", "    print(f\"  {row['feature']:<30} VIF: {row['VIF']:>8.2f} ({status})\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# Linear Regression Analysis\n", "Linear regression model with ABI_vs_Segment_PTC_Index_Agg as target variable and feature importance analysis.\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target variable: ABI_vs_Segment_PTC_Index_Agg\n", "Features: ['ABI_Mechanic', 'Overlapping', 'Same_Week', 'After', 'Before', 'Avg_Temp', 'ABI_Coverage']\n"]}, {"ename": "KeyError", "evalue": "\"['After', 'Before'] not in index\"", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[23], line 22\u001b[0m\n\u001b[0;32m     19\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFeatures: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfeature_cols\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     21\u001b[0m \u001b[38;5;66;03m# Create regression dataset\u001b[39;00m\n\u001b[1;32m---> 22\u001b[0m lr_data \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[43mfeature_cols\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43mtarget_col\u001b[49m\u001b[43m]\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mcopy()\n\u001b[0;32m     24\u001b[0m \u001b[38;5;66;03m# Encode categorical variables for regression\u001b[39;00m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m col \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mABI_Mechanic\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSame_Week\u001b[39m\u001b[38;5;124m'\u001b[39m]:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\frame.py:3902\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3900\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n\u001b[0;32m   3901\u001b[0m         key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(key)\n\u001b[1;32m-> 3902\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_indexer_strict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcolumns\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m   3904\u001b[0m \u001b[38;5;66;03m# take() does not accept boolean indexers\u001b[39;00m\n\u001b[0;32m   3905\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(indexer, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mbool\u001b[39m:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\indexes\\base.py:6114\u001b[0m, in \u001b[0;36mIndex._get_indexer_strict\u001b[1;34m(self, key, axis_name)\u001b[0m\n\u001b[0;32m   6111\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   6112\u001b[0m     keyarr, indexer, new_indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reindex_non_unique(keyarr)\n\u001b[1;32m-> 6114\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_raise_if_missing\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkeyarr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindexer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis_name\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   6116\u001b[0m keyarr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtake(indexer)\n\u001b[0;32m   6117\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, Index):\n\u001b[0;32m   6118\u001b[0m     \u001b[38;5;66;03m# GH 42790 - Preserve name from an Index\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\lib\\site-packages\\pandas\\core\\indexes\\base.py:6178\u001b[0m, in \u001b[0;36mIndex._raise_if_missing\u001b[1;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[0;32m   6175\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNone of [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m] are in the [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00maxis_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m]\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m   6177\u001b[0m not_found \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(ensure_index(key)[missing_mask\u001b[38;5;241m.\u001b[39mnonzero()[\u001b[38;5;241m0\u001b[39m]]\u001b[38;5;241m.\u001b[39munique())\n\u001b[1;32m-> 6178\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnot_found\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not in index\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: \"['After', 'Before'] not in index\""]}], "source": ["# Prepare data for Linear Regression\n", "target_col = 'ABI_vs_Segment_PTC_Index_Agg'\n", "feature_cols = [\n", "    'ABI_Mechanic',\n", "    'Overlapping', \n", "    'Same_Week',\n", "    'After',\n", "    'Before',\n", "    'Avg_Temp',\n", "    'ABI_Coverage'\n", "]\n", "\n", "# Check if target column exists in the original dataframe\n", "if target_col not in df.columns:\n", "    print(f\"Warning: {target_col} not found in data. Available columns:\")\n", "    print([col for col in df.columns if 'PTC' in col or 'Index' in col])\n", "else:\n", "    print(f\"Target variable: {target_col}\")\n", "    print(f\"Features: {feature_cols}\")\n", "    \n", "# Create regression dataset\n", "lr_data = df[feature_cols + [target_col]].copy()\n", "\n", "# Encode categorical variables for regression\n", "for col in ['ABI_Mechanic', 'Same_Week']:\n", "    if col in lr_data.columns and lr_data[col].dtype == 'object':\n", "        le = LabelEncoder()\n", "        lr_data[col] = le.fit_transform(lr_data[col].astype(str))\n", "\n", "# Ensure all columns are numeric\n", "for col in lr_data.columns:\n", "    lr_data[col] = pd.to_numeric(lr_data[col], errors='coerce')\n", "\n", "# Drop rows with missing values\n", "lr_data = lr_data.dropna()\n", "\n", "print(f\"\\nRegression dataset shape: {lr_data.shape}\")\n", "print(f\"Target variable stats:\\n{lr_data[target_col].describe()}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data and train Linear Regression model\n", "X = lr_data[feature_cols]\n", "y = lr_data[target_col]\n", "\n", "# Split into train and test sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Train Linear Regression model\n", "lr_model = LinearRegression()\n", "lr_model.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred_train = lr_model.predict(X_train)\n", "y_pred_test = lr_model.predict(X_test)\n", "\n", "# Calculate metrics\n", "train_r2 = r2_score(y_train, y_pred_train)\n", "test_r2 = r2_score(y_test, y_pred_test)\n", "train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))\n", "test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))\n", "train_mae = mean_absolute_error(y_train, y_pred_train)\n", "test_mae = mean_absolute_error(y_test, y_pred_test)\n", "\n", "print(\"Linear Regression Model Performance:\")\n", "print(\"=\" * 50)\n", "print(f\"Training R² Score: {train_r2:.4f}\")\n", "print(f\"Test R² Score: {test_r2:.4f}\")\n", "print(f\"Training RMSE: {train_rmse:.4f}\")\n", "print(f\"Test RMSE: {test_rmse:.4f}\")\n", "print(f\"Training MAE: {train_mae:.4f}\")\n", "print(f\"Test MAE: {test_mae:.4f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature Importance Analysis\n", "coefficients = lr_model.coef_\n", "intercept = lr_model.intercept_\n", "\n", "# Create feature importance dataframe\n", "importance_df = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Coefficient': coefficients,\n", "    'Abs_Coefficient': np.abs(coefficients)\n", "}).sort_values('Abs_Coefficient', ascending=False)\n", "\n", "print(\"Feature Importance (Linear Regression Coefficients):\")\n", "print(\"=\" * 60)\n", "print(f\"Intercept: {intercept:.4f}\")\n", "print()\n", "print(\"Features ranked by absolute coefficient magnitude:\")\n", "for _, row in importance_df.iterrows():\n", "    direction = \"positive\" if row['Coefficient'] > 0 else \"negative\"\n", "    print(f\"{row['Feature']:<20} Coef: {row['Coefficient']:>8.4f} ({direction} impact)\")\n", "\n", "# Calculate standardized coefficients for better comparison\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "lr_scaled = LinearRegression()\n", "lr_scaled.fit(X_scaled, y)\n", "\n", "std_importance_df = pd.DataFrame({\n", "    'Feature': feature_cols,\n", "    'Std_Coefficient': lr_scaled.coef_,\n", "    'Abs_Std_Coefficient': np.abs(lr_scaled.coef_)\n", "}).sort_values('Abs_Std_Coefficient', ascending=False)\n", "\n", "print(\"\\nStandardized Feature Importance (for fair comparison):\")\n", "print(\"=\" * 60)\n", "for _, row in std_importance_df.iterrows():\n", "    direction = \"positive\" if row['Std_Coefficient'] > 0 else \"negative\"\n", "    print(f\"{row['Feature']:<20} Std Coef: {row['Std_Coefficient']:>8.4f} ({direction})\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization: Linear Regression Plots\n", "plt.style.use('default')\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Linear Regression Analysis: ABI vs Segment PTC Index', fontsize=16, fontweight='bold')\n", "\n", "# Plot 1: Actual vs Predicted (Test Set)\n", "axes[0, 0].scatter(y_test, y_pred_test, alpha=0.6, color='blue', s=50)\n", "axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "axes[0, 0].set_xlabel('Actual Values')\n", "axes[0, 0].set_ylabel('Predicted Values')\n", "axes[0, 0].set_title(f'Actual vs Predicted (Test Set)\\nR² = {test_r2:.3f}')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Resi<PERSON><PERSON> vs Predicted\n", "residuals = y_test - y_pred_test\n", "axes[0, 1].scatter(y_pred_test, residuals, alpha=0.6, color='green', s=50)\n", "axes[0, 1].axhline(y=0, color='r', linestyle='--', lw=2)\n", "axes[0, 1].set_xlabel('Predicted Values')\n", "axes[0, 1].set_ylabel('Residuals')\n", "axes[0, 1].set_title('Residuals vs Predicted')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Feature Importance (Raw Coefficients)\n", "bars1 = axes[1, 0].barh(importance_df['Feature'], importance_df['Coefficient'], \n", "                       color=['red' if x < 0 else 'blue' for x in importance_df['Coefficient']])\n", "axes[1, 0].set_xlabel('Coefficient Value')\n", "axes[1, 0].set_title('Feature Importance (Raw Coefficients)')\n", "axes[1, 0].grid(True, alpha=0.3, axis='x')\n", "\n", "# Plot 4: Feature Importance (Standardized Coefficients)\n", "bars2 = axes[1, 1].barh(std_importance_df['Feature'], std_importance_df['Std_Coefficient'],\n", "                       color=['red' if x < 0 else 'blue' for x in std_importance_df['Std_Coefficient']])\n", "axes[1, 1].set_xlabel('Standardized Coefficient')\n", "axes[1, 1].set_title('Feature Importance (Standardized)')\n", "axes[1, 1].grid(True, alpha=0.3, axis='x')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Additional Feature Importance Visualization\n", "fig, axes = plt.subplots(1, 2, figsize=(16, 6))\n", "\n", "# Feature importance magnitude comparison\n", "combined_importance = pd.merge(\n", "    importance_df[['Feature', 'Abs_Coefficient']], \n", "    std_importance_df[['Feature', 'Abs_Std_Coefficient']], \n", "    on='Feature'\n", ")\n", "\n", "# Plot raw vs standardized importance\n", "x_pos = np.arange(len(combined_importance))\n", "width = 0.35\n", "\n", "bars1 = axes[0].bar(x_pos - width/2, combined_importance['Abs_Coefficient'], width, \n", "                   label='Raw Coefficients', alpha=0.8, color='skyblue')\n", "bars2 = axes[0].bar(x_pos + width/2, combined_importance['Abs_Std_Coefficient'], width,\n", "                   label='Standardized Coefficients', alpha=0.8, color='lightcoral')\n", "\n", "axes[0].set_xlabel('Features')\n", "axes[0].set_ylabel('Absolute Coefficient Value')\n", "axes[0].set_title('Feature Importance Comparison')\n", "axes[0].set_xticks(x_pos)\n", "axes[0].set_xticklabels(combined_importance['Feature'], rotation=45, ha='right')\n", "axes[0].legend()\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Correlation heatmap between features and target\n", "corr_data = lr_data[feature_cols + [target_col]].corr()\n", "target_corr = corr_data[target_col].drop(target_col).abs().sort_values(ascending=False)\n", "\n", "im = axes[1].imshow([target_corr.values], aspect='auto', cmap='RdYlBu_r', vmin=-1, vmax=1)\n", "axes[1].set_xticks(range(len(target_corr)))\n", "axes[1].set_xticklabels(target_corr.index, rotation=45, ha='right')\n", "axes[1].set_yticks([0])\n", "axes[1].set_yticklabels(['Correlation with Target'])\n", "axes[1].set_title('Feature Correlation with Target Variable')\n", "\n", "# Add correlation values on the heatmap\n", "for i, val in enumerate(target_corr.values):\n", "    axes[1].text(i, 0, f'{val:.3f}', ha='center', va='center', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary Report: Linear Regression & Feature Importance\n", "print(\"=\"*80)\n", "print(\"LINEAR REGRESSION ANALYSIS SUMMARY\")\n", "print(\"=\"*80)\n", "\n", "print(f\"\\nModel Performance:\")\n", "print(f\"  • R² Score (Test): {test_r2:.4f} ({test_r2*100:.1f}% variance explained)\")\n", "print(f\"  • RMSE (Test): {test_rmse:.4f}\")\n", "print(f\"  • MAE (Test): {test_mae:.4f}\")\n", "\n", "print(f\"\\nTop 3 Most Important Features (by standardized coefficients):\")\n", "for i, (_, row) in enumerate(std_importance_df.head(3).iterrows(), 1):\n", "    impact = \"increases\" if row['Std_Coefficient'] > 0 else \"decreases\"\n", "    print(f\"  {i}. {row['Feature']}: {impact} target by {abs(row['Std_Coefficient']):.4f} std units\")\n", "\n", "print(f\"\\nFeature Impact Direction:\")\n", "for _, row in std_importance_df.iterrows():\n", "    direction = \"↑ Positive\" if row['Std_Coefficient'] > 0 else \"↓ Negative\"\n", "    magnitude = \"High\" if abs(row['Std_Coefficient']) > 0.5 else \"Medium\" if abs(row['Std_Coefficient']) > 0.2 else \"Low\"\n", "    print(f\"  • {row['Feature']:<20} {direction:<12} ({magnitude} impact)\")\n", "\n", "print(f\"\\nMulticollinearity vs Feature Importance:\")\n", "print(f\"  Features with high VIF (>5) that are also important:\")\n", "if 'vif_data' in locals():\n", "    high_vif_features = set(vif_data[vif_data['VIF'] > 5]['feature'].tolist())\n", "    important_features = set(std_importance_df.head(3)['Feature'].tolist())\n", "    overlap = high_vif_features.intersection(important_features)\n", "    if overlap:\n", "        for feature in overlap:\n", "            vif_val = vif_data[vif_data['feature'] == feature]['VIF'].iloc[0]\n", "            importance_val = std_importance_df[std_importance_df['Feature'] == feature]['Std_Coefficient'].iloc[0]\n", "            print(f\"    - {feature}: VIF={vif_val:.2f}, Importance={importance_val:.4f}\")\n", "    else:\n", "        print(\"    - No overlap between high VIF and top important features\")\n", "else:\n", "    print(\"    - VIF analysis not available for comparison\")\n", "\n", "print(\"=\"*80)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}