#!/usr/bin/env python3
"""
Test MODEL 5: Brand + Retailer Crossed Random Effects
Without modifying existing files - just to see the output
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.regression.mixed_linear_model import MixedLM
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def main():
    print("="*80)
    print("MODEL 5: BRAND + RETAILER CROSSED RANDOM EFFECTS TEST")
    print("="*80)
    
    # Load and prepare data exactly like in your notebook
    df = pd.read_csv('demelted_data.csv')
    
    # Create variables
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    
    # Create standardized variables
    scaler = StandardScaler()
    df['ABI_Duration_Days'] = (pd.to_datetime(df['ABI End']) - pd.to_datetime(df['ABI Start'])).dt.days
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    
    for var in ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    # Extract brand from SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    df['KSM'] = df['KSM'].astype(int)
    
    # Prepare model data (without mechanic as per your current approach)
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                 'Before', 'After', 'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 
                 'Retailer', 'Brand', 'KSM']
    
    df_model = df[model_vars].dropna()
    print(f"Model data shape: {df_model.shape}")
    
    # Check data distribution
    print(f"\nData distribution:")
    print(f"Retailers: {df_model['Retailer'].nunique()} unique")
    print(f"Brands: {df_model['Brand'].nunique()} unique")
    print(f"Retailer distribution: {df_model['Retailer'].value_counts().head()}")
    print(f"Brand distribution: {df_model['Brand'].value_counts()}")
    
    # Base formula (without mechanic)
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std + 
                     Before + After + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std + KSM"""
    
    print(f"\nBase formula: {base_formula}")
    
    try:
        print("\n" + "-"*60)
        print("FITTING MODEL 5: BRAND + RETAILER CROSSED RANDOM EFFECTS")
        print("-"*60)
        
        # Create crossed random effects model
        # This accounts for both retailer AND brand random effects simultaneously
        model_crossed = MixedLM.from_formula(
            base_formula, 
            df_model,
            groups=df_model["Retailer"],  # Primary grouping by retailer
            re_formula="1",               # Random intercept for retailer
            vc_formula={"Brand": "0 + C(Brand)"}  # Variance component for brand
        ).fit()
        
        print("✅ MODEL 5 FITTED SUCCESSFULLY!")
        print("\nModel Summary:")
        print(model_crossed.summary())
        
        print(f"\nModel Fit Statistics:")
        print(f"AIC: {model_crossed.aic:.2f}")
        print(f"BIC: {model_crossed.bic:.2f}")
        print(f"Log-Likelihood: {model_crossed.llf:.4f}")
        
        print(f"\n" + "="*60)
        print("KEY TIMING COEFFICIENTS - MODEL 5")
        print("="*60)
        
        before_coeff = model_crossed.params['Before']
        after_coeff = model_crossed.params['After']
        before_p = model_crossed.pvalues['Before']
        after_p = model_crossed.pvalues['After']
        
        print(f"Before coefficient: {before_coeff:.4f} (p={before_p:.4f})")
        print(f"After coefficient: {after_coeff:.4f} (p={after_p:.4f})")
        print(f"Before > After: {before_coeff > after_coeff}")
        
        if before_p < 0.05:
            print(f"✅ Before effect is statistically significant")
        else:
            print(f"❌ Before effect is NOT statistically significant")
            
        if after_p < 0.05:
            print(f"✅ After effect is statistically significant")
        else:
            print(f"❌ After effect is NOT statistically significant")
        
        print(f"\n" + "="*60)
        print("RANDOM EFFECTS ANALYSIS")
        print("="*60)
        
        print(f"Retailer random effect variance: {model_crossed.cov_re.iloc[0,0]:.4f}")
        
        # Extract brand variance components
        print(f"\nBrand variance components:")
        if hasattr(model_crossed, 'vcomp'):
            for i, comp in enumerate(model_crossed.vcomp):
                print(f"Brand variance component {i+1}: {comp:.4f}")
        
        print(f"\nResidual variance: {model_crossed.scale:.4f}")
        
        # Calculate ICC (Intraclass Correlation)
        total_var = model_crossed.cov_re.iloc[0,0] + model_crossed.scale
        if hasattr(model_crossed, 'vcomp') and len(model_crossed.vcomp) > 0:
            total_var += sum(model_crossed.vcomp)
        
        retailer_icc = model_crossed.cov_re.iloc[0,0] / total_var
        print(f"\nRetailer ICC: {retailer_icc:.4f}")
        print(f"This means {retailer_icc*100:.1f}% of variance is due to retailer differences")
        
        if hasattr(model_crossed, 'vcomp') and len(model_crossed.vcomp) > 0:
            brand_icc = sum(model_crossed.vcomp) / total_var
            print(f"Brand ICC: {brand_icc:.4f}")
            print(f"This means {brand_icc*100:.1f}% of variance is due to brand differences")
        
        print(f"\n" + "="*60)
        print("BUSINESS INTERPRETATION - MODEL 5")
        print("="*60)
        
        print("This model accounts for:")
        print("1. Retailer-specific baseline differences (random intercepts)")
        print("2. Brand-specific variance components")
        print("3. The interaction between retailer and brand effects")
        print("\nThis gives you the 'cleanest' estimate of timing effects")
        print("after controlling for both retailer AND brand heterogeneity.")
        
        if before_coeff > after_coeff:
            print(f"\n✅ RESULT: Even after controlling for both retailer and brand effects,")
            print(f"   competitor promotions 1 week BEFORE are better than 1 week AFTER")
            print(f"   (Before: {before_coeff:.3f} vs After: {after_coeff:.3f})")
        else:
            print(f"\n❌ RESULT: After controlling for retailer and brand effects,")
            print(f"   competitor promotions 1 week AFTER appear better than 1 week BEFORE")
            print(f"   (After: {after_coeff:.3f} vs Before: {before_coeff:.3f})")
        
        print(f"\n" + "="*60)
        print("RECOMMENDATION")
        print("="*60)
        
        if before_p < 0.1 or after_p < 0.1:
            print("✅ At least one timing effect is marginally significant.")
            print("   This model provides good evidence for timing effects.")
        else:
            print("❌ Neither timing effect is statistically significant.")
            print("   The timing effects may be too weak to detect reliably.")
            
        print(f"\nModel 5 is the most sophisticated approach as it accounts for")
        print(f"both retailer AND brand heterogeneity simultaneously.")
        
    except Exception as e:
        print(f"❌ ERROR FITTING MODEL 5: {e}")
        import traceback
        traceback.print_exc()
        
        print(f"\nTrying alternative specification...")
        
        try:
            # Alternative: Use brand as second grouping variable
            print(f"\nTrying alternative crossed random effects specification...")
            
            # Create a combined grouping variable
            df_model['Retailer_Brand'] = df_model['Retailer'].astype(str) + "_" + df_model['Brand'].astype(str)
            
            model_alt = MixedLM.from_formula(
                base_formula, 
                df_model,
                groups=df_model["Retailer_Brand"]
            ).fit()
            
            print("✅ ALTERNATIVE MODEL FITTED!")
            print(f"Before coefficient: {model_alt.params['Before']:.4f} (p={model_alt.pvalues['Before']:.4f})")
            print(f"After coefficient: {model_alt.params['After']:.4f} (p={model_alt.pvalues['After']:.4f})")
            print(f"Before > After: {model_alt.params['Before'] > model_alt.params['After']}")
            
        except Exception as e2:
            print(f"❌ Alternative also failed: {e2}")

if __name__ == "__main__":
    main()
