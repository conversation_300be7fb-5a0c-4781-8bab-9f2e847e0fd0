# Memory Document: Hierarchical Model Data Analysis

## Project Overview
- **Working Directory**: `/mnt/c/Users/<USER>/OneDrive - Anheuser-Busch InBev/Documents/Swagat/Hierarchical Model/Hierarchical_V4`
- **Primary Dataset**: `same_week_start_ads.csv`
- **Target Variable**: `ABI MS Promo Uplift - rel` (Column 25, 0-indexed)

## Dataset Summary
- **Total Records**: 771 rows (including header)
- **Data Records**: 770 rows (excluding header)
- **Target Variable**: ABI MS Promo Uplift - rel (relative promotional uplift)

## Data Structure
The dataset contains promotional data with the following key columns:
- Retailer information (Retailer, ABI PromoID, ABI SKU)
- Promotion details (ABI Start, ABI End, ABI Coverage, ABI Mechanic, ABI Depth)
- Competitor information (Competitor SKU, Coverage, Mechanic, Depth)
- Timing flags (Same Week, 1 wk after, 2 wk after, 1 wk before, 2 wk before)
- **Target Variable**: ABI MS Promo Uplift - rel (promotional uplift relative measure)
- Additional metrics (PTC indices, distributions, aggregates)

## Target Variable Analysis
### Distribution Statistics
- **Min**: 0.635801
- **Max**: 620.078594
- **Mean**: 5.515043
- **Median**: 2.524389
- **Standard Deviation**: 27.577546

### Percentile Distribution
- **5th percentile**: 0.986477
- **10th percentile**: 1.163006
- **25th percentile**: 1.645214
- **50th percentile**: 2.524389
- **75th percentile**: 3.774047
- **90th percentile**: 5.743755
- **95th percentile**: 7.760783
- **97.5th percentile**: 15.347710
- **99th percentile**: 83.554007

## Filtering Requirements
**Objective**: Remove rows above the 95th percentile (keeping data at or below 95th percentile)

### Filtering Thresholds
- **Upper Bound**: 7.760783 (95th percentile)
- **Lower Bound**: None (keep all values from minimum upward)

### Impact Assessment
- **Rows to Remove**: 38 rows (above 95th percentile)
- **Rows to Keep**: 732 rows (at or below 95th percentile)
- **Retention Rate**: 95.06% of data will be kept

## Filtering Implementation Results
**✓ COMPLETED**: Filtering successfully applied on 2025-07-10

### Final Results
- **Original dataset**: 770 data rows
- **Filtered dataset**: 732 data rows (38 rows removed)
- **Percentage kept**: 95.06%
- **Output file**: `same_week_start_ads_filtered_95th_percentile.csv`

### Validation Results
- **Min value in filtered data**: 0.635801
- **Max value in filtered data**: 7.760783 (exactly at 95th percentile)
- **Mean of filtered data**: 2.735765
- **Median of filtered data**: 2.398847
- **✓ No values above threshold**: Confirmed

## Key Findings
1. The target variable shows significant right skewness (mean > median)
2. There are outliers with values much higher than the median (max = 620.08 vs median = 2.52)
3. The 95th percentile filtering effectively removed extreme outliers while retaining 95% of the data
4. The data quality appears good with no missing values in the target variable
5. Filtering reduced the mean from 5.515 to 2.736, bringing it closer to the median