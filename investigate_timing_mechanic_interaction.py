#!/usr/bin/env python3
"""
Investigate the timing-mechanic interaction more deeply to find the real solution
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
import statsmodels.formula.api as smf
from sklearn.preprocessing import StandardScaler

def main():
    # Load and prepare data
    df = pd.read_csv('demelted_data.csv')
    
    df['Before'] = df['1 wk before'].fillna(0).astype(int)
    df['After'] = df['1 wk after'].fillna(0).astype(int)
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    
    # Create standardized variables
    scaler = StandardScaler()
    df['ABI_Duration_Days'] = (pd.to_datetime(df['ABI End']) - pd.to_datetime(df['ABI Start'])).dt.days
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    
    for var in ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    # Prepare model data
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                 'Before', 'After', 'Avg_Temp_std', 'ABI_vs_Segment_PTC_Index_Agg_std', 
                 'ABI Mechanic', 'Retailer', 'KSM']
    
    df_model = df[model_vars].dropna()
    print(f"Model data shape: {df_model.shape}")
    
    print("\n" + "="*80)
    print("DEEP DIVE: TIMING-MECHANIC INTERACTION ANALYSIS")
    print("="*80)
    
    # 1. Check if mechanic choice is endogenous to timing
    print("\n1. IS MECHANIC CHOICE DRIVEN BY TIMING?")
    print("-" * 50)
    
    # Test if Before/After predict mechanic choice
    print("Testing if timing predicts mechanic choice...")
    
    # Create dummy variables for mechanics
    mechanic_dummies = pd.get_dummies(df_model['ABI Mechanic'], prefix='Mech')
    
    for mech in mechanic_dummies.columns:
        if mechanic_dummies[mech].sum() > 10:  # Only test mechanics with enough observations
            try:
                formula = f"{mech} ~ Before + After + ABI_Coverage_std + ABI_Duration_Days_std"
                model = smf.ols(formula, data=pd.concat([df_model, mechanic_dummies], axis=1)).fit()
                
                before_p = model.pvalues.get('Before', 1.0)
                after_p = model.pvalues.get('After', 1.0)
                
                print(f"{mech}: Before p={before_p:.3f}, After p={after_p:.3f}")
                
                if before_p < 0.05 or after_p < 0.05:
                    print(f"  ⚠️  {mech} is significantly predicted by timing!")
                    
            except Exception as e:
                print(f"Error testing {mech}: {e}")
    
    # 2. Check the actual data patterns more carefully
    print("\n2. DETAILED TIMING-MECHANIC PATTERNS")
    print("-" * 50)
    
    # Create timing categories
    df_model['Timing_Category'] = 'Neither'
    df_model.loc[df_model['Before'] == 1, 'Timing_Category'] = 'Before_Only'
    df_model.loc[df_model['After'] == 1, 'Timing_Category'] = 'After_Only'
    df_model.loc[(df_model['Before'] == 1) & (df_model['After'] == 1), 'Timing_Category'] = 'Both'
    
    print("Timing category distribution:")
    print(df_model['Timing_Category'].value_counts())
    
    print("\nUplift by timing category:")
    timing_uplift = df_model.groupby('Timing_Category')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std']).round(3)
    print(timing_uplift)
    
    print("\nMechanic distribution by timing category:")
    timing_mechanic_crosstab = pd.crosstab(df_model['Timing_Category'], df_model['ABI Mechanic'], normalize='index')
    print(timing_mechanic_crosstab.round(3))
    
    # 3. Test different model specifications
    print("\n3. TESTING DIFFERENT MODEL SPECIFICATIONS")
    print("-" * 50)
    
    models_to_test = {
        'baseline': "ABI_MS_Uplift_Rel ~ Before + After",
        'with_controls': "ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + ABI_Duration_Days_std + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std",
        'with_mechanic': "ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + ABI_Duration_Days_std + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)",
        'with_interactions': "ABI_MS_Uplift_Rel ~ Before + After + ABI_Coverage_std + ABI_Duration_Days_std + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic) + Before:C(ABI_Mechanic) + After:C(ABI_Mechanic)",
        'timing_categories': "ABI_MS_Uplift_Rel ~ C(Timing_Category) + ABI_Coverage_std + ABI_Duration_Days_std + Avg_Temp_std + ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"
    }
    
    results = {}
    
    for name, formula in models_to_test.items():
        try:
            print(f"\nTesting {name}:")
            model = smf.ols(formula, data=df_model).fit()
            
            if 'Before' in model.params.index and 'After' in model.params.index:
                before_coeff = model.params['Before']
                after_coeff = model.params['After']
                before_p = model.pvalues['Before']
                after_p = model.pvalues['After']
                
                print(f"  Before: {before_coeff:.4f} (p={before_p:.3f})")
                print(f"  After: {after_coeff:.4f} (p={after_p:.3f})")
                print(f"  Before > After: {before_coeff > after_coeff}")
                print(f"  R-squared: {model.rsquared:.3f}")
                
                results[name] = {
                    'before': before_coeff,
                    'after': after_coeff,
                    'before_p': before_p,
                    'after_p': after_p,
                    'rsquared': model.rsquared
                }
            elif 'Timing_Category' in formula:
                print("  Timing category coefficients:")
                for param in model.params.index:
                    if 'Timing_Category' in param:
                        print(f"    {param}: {model.params[param]:.4f} (p={model.pvalues[param]:.3f})")
                print(f"  R-squared: {model.rsquared:.3f}")
                
        except Exception as e:
            print(f"  Error: {e}")
    
    # 4. Check for Simpson's Paradox
    print("\n4. SIMPSON'S PARADOX CHECK")
    print("-" * 50)
    
    print("Checking if the relationship reverses within mechanic groups...")
    
    for mechanic in df_model['ABI Mechanic'].unique():
        if df_model[df_model['ABI Mechanic'] == mechanic].shape[0] > 20:  # Only check mechanics with enough data
            mech_data = df_model[df_model['ABI Mechanic'] == mechanic]
            
            before_0 = mech_data[mech_data['Before'] == 0]['ABI_MS_Uplift_Rel']
            before_1 = mech_data[mech_data['Before'] == 1]['ABI_MS_Uplift_Rel']
            after_0 = mech_data[mech_data['After'] == 0]['ABI_MS_Uplift_Rel']
            after_1 = mech_data[mech_data['After'] == 1]['ABI_MS_Uplift_Rel']
            
            if len(before_0) > 0 and len(before_1) > 0 and len(after_0) > 0 and len(after_1) > 0:
                before_diff = before_1.mean() - before_0.mean()
                after_diff = after_1.mean() - after_0.mean()
                
                print(f"\n{mechanic}:")
                print(f"  Before effect: {before_diff:.3f}")
                print(f"  After effect: {after_diff:.3f}")
                print(f"  Before > After: {before_diff > after_diff}")
    
    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    
    print("\nBased on the analysis above, the best approach is likely:")
    print("1. Use interaction terms to capture timing-mechanic relationships")
    print("2. Or use timing categories instead of separate Before/After variables")
    print("3. The issue is NOT that we need to remove variables, but that we need")
    print("   to model the relationships between timing and mechanic correctly")

if __name__ == "__main__":
    main()
